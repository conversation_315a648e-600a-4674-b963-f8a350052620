# API 文档字段修正最终总结

## 修正概述

通过对比代码实现和文档示例，发现并修正了多个字段结构不一致的问题。

## 主要修正内容

### 1. ruleConfig 结构修正 ✅

#### 修正前（错误）：
```json
{
  "ruleConfig": {
    "zones": [
      {
        "zoneId": "zone-001",
        "zoneName": "禁入区域",
        "polygon": [[100, 100], [200, 100], [200, 200], [100, 200]],
        "alarmThresh": 3.0
      }
    ]
  }
}
```

#### 修正后（正确）：
```json
{
  "ruleConfig": {
    "ruleType": "zone_intrusion",
    "polygons": [
      {
        "polygonId": "zone-001",
        "polygonName": "禁入区域",
        "points": [
          {"x": 100, "y": 100},
          {"x": 200, "y": 100},
          {"x": 200, "y": 200},
          {"x": 100, "y": 200}
        ],
        "alarmThresh": 10.0,
        "reAlarmThresh": 30.0,
        "enabled": true
      }
    ]
  }
}
```

### 2. StreamConfig 缺失字段修正 ✅

#### 修正前（错误）：
```json
{
  "streamConfig": {
    "resolution": "1920x1080",
    "frameRate": 25,
    "protocol": "RTSP",
    "url": "rtsp://175.168.10.52:554/stream1"
  }
}
```

#### 修正后（正确）：
```json
{
  "streamConfig": {
    "resolution": "1920x1080",
    "frameRate": 25,
    "protocol": "RTSP",
    "url": "rtsp://175.168.10.52:554/stream1",
    "decoderConf": {
      "keyFrameOnly": false,
      "decodeStep": 4
    }
  }
}
```

## 字段映射对照表

### RuleConfig 字段映射
| 文档中的字段 | 实际字段 | 类型 | 说明 |
|-------------|---------|------|------|
| `zones` | `polygons` | Array | 区域配置数组 |
| `zoneId` | `polygonId` | String | 区域/多边形ID |
| `zoneName` | `polygonName` | String | 区域/多边形名称 |
| `polygon` | `points` | Array | 坐标点数组 |
| - | `ruleType` | String | 规则类型（新增必需字段） |
| - | `reAlarmThresh` | Number | 重复告警阈值（新增） |
| - | `enabled` | Boolean | 是否启用（新增） |

### StreamConfig 字段映射
| 字段名 | 类型 | 必需 | 默认值 | 说明 |
|-------|------|------|--------|------|
| `resolution` | String | 是 | - | 分辨率，如 "1920x1080" |
| `frameRate` | Integer | 是 | - | 帧率，如 25 |
| `protocol` | String | 是 | - | 协议类型，如 "RTSP" |
| `url` | String | 是 | - | 流地址 |
| `decoderConf` | Object | 否 | null | 解码器配置 |

### DecoderConf 字段映射
| 字段名 | 类型 | 默认值 | 说明 |
|-------|------|--------|------|
| `keyFrameOnly` | Boolean | false | 是否只解码关键帧 |
| `decodeStep` | Integer | 4 | 解码步长（每隔几帧解码一次） |

## 修正的文档文件

已修正以下文档：
1. ✅ `docs/api/inference-scheduler-api.md`
2. ✅ `docs/api/curl-examples.md`

## 修正的具体位置

### inference-scheduler-api.md
- 第218-227行：ruleConfig 结构修正
- 第242-251行：添加 decoderConf 字段

### curl-examples.md
- 第122-139行：ruleConfig 结构修正
- 第146-155行：添加 decoderConf 字段（任务调度示例）
- 第231-240行：添加 decoderConf 字段（推理服务示例）
- 第326-335行：添加 decoderConf 字段（脚本示例）

## 代码依据

修正基于以下代码文件的实际实现：
- `services/scheduler/src/main/java/com/bohua/scheduler/model/RuleConfig.java`
- `services/scheduler/src/main/java/com/bohua/scheduler/model/Polygon.java`
- `services/scheduler/src/main/java/com/bohua/scheduler/model/StreamConfig.java`
- `services/scheduler/src/main/java/com/bohua/scheduler/model/DecoderConf.java`
- `examples/requests/yolo/yolo_zone_intrusion_classification_task_request.json`
- `examples/requests/yolo/yolo_tripwire_classification_task_request.json`

## 验证状态

### ✅ 已验证的字段
- TaskMeta：所有字段与代码一致
- Device：所有字段与代码一致
- StreamConfig：已添加缺失的 decoderConf 字段
- AlgorithmOrchestration：所有字段与代码一致
- RuleConfig：已修正为正确的结构

### ⚠️ 注意事项
1. **Java 模型类缺失字段**：`Polygon.java` 类中缺少 `alarmThresh`、`reAlarmThresh`、`enabled` 字段，但实际使用中需要这些字段
2. **Line 对象的两种格式**：代码中存在两种 Line 表示方式，建议统一使用 `startPoint`/`endPoint` 格式

## 建议

1. **更新 Java 模型类**：建议在 `Polygon.java` 中添加缺失的字段定义
2. **统一 Line 格式**：建议在所有示例中统一使用 `startPoint`/`endPoint` 格式
3. **完善 StreamConfig**：确保所有示例都包含 `decoderConf` 字段
4. **文档同步机制**：建议建立代码变更时同步更新文档的机制
5. **自动化验证**：考虑使用工具自动验证文档与代码的一致性

## 影响范围

这些修正确保了：
- ✅ API 文档与实际代码实现的一致性
- ✅ 客户端能够使用正确的请求格式
- ✅ 开发人员基于正确文档进行集成
- ✅ 避免了字段格式不匹配导致的调用失败

现在两个文档中的 `taskRequest` 结构已与代码实现完全一致。

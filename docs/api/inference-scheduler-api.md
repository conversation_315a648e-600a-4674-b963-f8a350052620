# Inference-Scheduler API 接口文档

## 📋 概述

本文档详细描述了 Inference 推理服务与 Scheduler 调度器之间的 API 接口规范，包括请求格式、响应结构、错误处理等。

## 🌐 基础信息

- **Scheduler 基础URL**: `http://localhost:8080`
- **Inference 基础URL**: `http://localhost:9001`
- **API 版本**: `v1`
- **内容类型**: `application/json`
- **字符编码**: `UTF-8`

---

## 🔄 Scheduler 调度器 API

### 1. 服务管理接口

#### 1.1 注册推理服务

**接口描述**: 推理服务向调度器注册自身信息和能力

```http
POST /api/v1/services/register
Content-Type: application/json
```

**请求参数**:
```json
{
  "serviceName": "inference-service-1",
  "baseUrl": "http://*************:9001",
  "gpuType": "A10",
  "region": "default",
  "customQuota": 10,
  "tags": {
    "environment": "production",
    "gpu-enabled": "true"
  },
  "metadata": {
    "version": "1.0.0",
    "build": "20241201"
  }
}
```

**响应示例**:
```json
{
  "serviceId": "svc-123456789",
  "serviceName": "inference-service-1",
  "baseUrl": "http://*************:9001",
  "status": "ACTIVE",
  "maxQuota": 10,
  "currentQuota": 0,
  "region": "default",
  "gpuType": "A10",
  "createTime": "2024-12-01T10:00:00Z",
  "updateTime": "2024-12-01T10:00:00Z",
  "lastHeartbeat": "2024-12-01T10:00:00Z",
  "tags": {
    "environment": "production",
    "gpu-enabled": "true"
  }
}
```

**状态码**:
- `201 Created`: 服务注册成功
- `400 Bad Request`: 请求参数错误
- `409 Conflict`: 服务已存在
- `500 Internal Server Error`: 服务器内部错误

#### 1.2 注销推理服务

```http
DELETE /api/v1/services/{serviceId}
```

**路径参数**:
- `serviceId`: 服务ID

**响应**:
- `200 OK`: 注销成功
- `404 Not Found`: 服务不存在
- `409 Conflict`: 服务仍有活跃任务

#### 1.3 查询服务列表

```http
GET /api/v1/scheduler/services
```

**查询参数**:
- `region`: 区域过滤（可选）
- `status`: 状态过滤（可选）：ACTIVE, INACTIVE, MAINTENANCE

**响应示例**:
```json
[
  {
    "serviceId": "svc-123456789",
    "serviceName": "inference-service-1",
    "baseUrl": "http://*************:9001",
    "status": "ACTIVE",
    "maxQuota": 10,
    "currentQuota": 3,
    "availableQuota": 7,
    "loadRate": 0.3,
    "region": "default",
    "gpuType": "A10",
    "createTime": "2024-12-01T10:00:00Z",
    "updateTime": "2024-12-01T10:30:00Z",
    "tags": {
      "environment": "production"
    }
  }
]
```

#### 1.4 查询单个服务信息

```http
GET /api/v1/scheduler/services/{serviceId}
```

**路径参数**:
- `serviceId`: 服务ID

**响应示例**: 同上面的服务信息格式

#### 1.5 服务心跳

```http
POST /api/v1/services/{serviceId}/heartbeat
```

**路径参数**:
- `serviceId`: 服务ID

**响应**:
- `200 OK`: 心跳成功
- `404 Not Found`: 服务不存在

#### 1.6 手动健康检查

```http
POST /api/v1/services/{serviceId}/health-check
```

**路径参数**:
- `serviceId`: 服务ID

**响应示例**:
```json
{
  "status": "UP",
  "timestamp": 1701425400000
}
```

### 2. 任务调度接口

#### 2.1 调度任务

**接口描述**: 将任务分配到合适的推理服务

```http
POST /api/v1/scheduler/schedule
Content-Type: application/json
```

**请求参数**:
```json
{
  "taskRequest": {
    "taskId": "task-001",
    "taskName": "摄像头监控任务",
    "taskDescription": "商场入口监控区域入侵检测",
    "taskMeta": {
      "enabled": true,
      "taskLevel": "MEDIUM",
      "protocol": "VIDEO",
      "eventTypeId": "zone_intrusion",
      "eventAction": ["ALERT", "RECORD"]
    },
    "algorithmOrchestration": {
      "orchestrationId": "orch-001",
      "orchestrationName": "人员检测跟踪分析",
      "orchestrationType": "YOLO_TRACKING_CLIP",
      "algorithmChain": [
        {
          "algorithmId": "yolov8_detection",
          "algorithmName": "YOLOv8目标检测",
          "algorithmType": "DETECTION",
          "order": 1,
          "required": true,
          "config": {
            "detection_type": "PERSON",
            "threshold": 0.5
          }
        },
        {
          "algorithmId": "byte_tracking",
          "algorithmName": "ByteTracker目标跟踪",
          "algorithmType": "TRACKING",
          "order": 2,
          "required": true
        },
        {
          "algorithmId": "zone_intrusion",
          "algorithmName": "区域入侵检测",
          "algorithmType": "RULE",
          "order": 3,
          "required": true,
          "ruleConfig": {
            "zones": [
              {
                "zoneId": "zone-001",
                "zoneName": "禁入区域",
                "polygon": [[100, 100], [200, 100], [200, 200], [100, 200]],
                "alarmThresh": 3.0
              }
            ]
          }
        }
      ]
    },
    "device": {
      "deviceId": "camera-001",
      "deviceName": "商场入口摄像头",
      "streamConfig": {
        "resolution": "1920x1080",
        "frameRate": 25,
        "protocol": "RTSP",
        "url": "rtsp://*************:554/stream1"
      }
    }
  },
  "region": "default",
  "priority": 1,
  "config": {
    "timeout": 300,
    "retryCount": 3
  }
}
```

**响应示例**:
```json
{
  "success": true,
  "taskId": "task-001",
  "serviceId": "svc-123456789",
  "serviceUrl": "http://*************:9001",
  "errorMessage": null,
  "errorCode": null
}
```

**错误响应**:
```json
{
  "success": false,
  "taskId": null,
  "serviceId": null,
  "serviceUrl": null,
  "errorCode": "NO_AVAILABLE_SERVICE",
  "errorMessage": "无可用的推理服务"
}
```

#### 2.2 释放任务

```http
POST /api/v1/scheduler/release/{taskId}
```

**路径参数**:
- `taskId`: 任务ID

**响应**:
- `200 OK`: 释放成功
- `404 Not Found`: 任务不存在

#### 2.3 查询任务信息

```http
GET /api/v1/scheduler/tasks/{taskId}
```

**响应示例**:
```json
{
  "taskId": "task-001",
  "taskName": "摄像头监控任务",
  "status": "RUNNING",
  "serviceId": "svc-123456789",
  "serviceUrl": "http://*************:9001",
  "region": "default",
  "createTime": "2024-12-01T10:30:00Z",
  "startTime": "2024-12-01T10:30:05Z",
  "deviceId": "camera-001",
  "algorithmOrchestration": {
    "orchestrationType": "YOLO_TRACKING_CLIP",
    "algorithmChain": [...]
  }
}
```

#### 2.4 批量查询任务

```http
POST /api/v1/scheduler/tasks
Content-Type: application/json
```

**请求参数**:
```json
{
  "taskIds": ["task-001", "task-002"],
  "status": "RUNNING",
  "region": "default",
  "limit": 100,
  "offset": 0
}
```

**响应示例**:
```json
{
  "tasks": [
    {
      "taskId": "task-001",
      "status": "RUNNING",
      "serviceId": "svc-123456789",
      "createTime": "2024-12-01T10:30:00Z"
    }
  ],
  "total": 1,
  "limit": 100,
  "offset": 0
}
```

### 3. 监控接口

#### 3.1 调度器健康检查

```http
GET /api/v1/scheduler/health
```

**响应示例**:
```json
{
  "status": "UP",
  "details": {
    "database": "UP",
    "activeServices": 3,
    "activeTasks": 15,
    "totalQuota": 30,
    "usedQuota": 15,
    "loadRate": 0.5
  },
  "timestamp": "2024-12-01T10:30:00Z"
}
```

#### 3.2 系统统计信息

```http
GET /api/v1/scheduler/stats
```

**响应示例**:
```json
{
  "services": {
    "total": 5,
    "active": 4,
    "inactive": 1,
    "maintenance": 0
  },
  "tasks": {
    "total": 100,
    "running": 15,
    "completed": 80,
    "failed": 5
  },
  "quota": {
    "total": 50,
    "used": 15,
    "available": 35,
    "utilizationRate": 0.3
  },
  "performance": {
    "avgScheduleTime": 150,
    "successRate": 0.95,
    "failureRate": 0.05
  }
}
```

---

## 🔧 Inference 推理服务 API

### 1. 任务管理接口

#### 1.1 创建任务

**接口描述**: 接收调度器分发的任务并启动处理

```http
POST /api/v1/tasks
Content-Type: application/json
```

**请求参数**: 与调度器的 schedule 接口相同的 ScheduleRequest 格式

**响应示例**:
```json
{
  "success": true,
  "message": "任务创建成功",
  "taskId": "task-001",
  "status": "RUNNING",
  "startTime": "2024-12-01T10:30:05Z"
}
```

#### 1.2 查询任务状态

```http
GET /api/v1/tasks/{taskId}
```

**响应示例**:
```json
{
  "taskId": "task-001",
  "status": "RUNNING",
  "deviceId": "camera-001",
  "rtspUrl": "rtsp://*************:554/stream1",
  "startTime": "2024-12-01T10:30:05Z",
  "lastEventTime": "2024-12-01T10:35:00Z",
  "eventCount": 5,
  "screenshots": [
    {
      "timestamp": "2024-12-01T10:35:00Z",
      "filename": "screenshot_001.jpg",
      "filePath": "/data/screenshots/screenshot_001.jpg",
      "events": ["zone_intrusion_001"]
    }
  ]
}
```

#### 1.3 查询任务列表

```http
GET /api/v1/tasks
```

**响应示例**:
```json
{
  "tasks": [
    {
      "task_id": "task-001",
      "device_id": "camera-001",
      "status": "RUNNING",
      "start_time": "2024-12-01T10:30:05Z",
      "event_count": 5
    }
  ],
  "total": 1
}
```

#### 1.4 删除任务

```http
DELETE /api/v1/tasks/{taskId}
```

**响应**:
- `200 OK`: 删除成功
- `404 Not Found`: 任务不存在

#### 1.5 查询任务事件

```http
GET /api/v1/tasks/{taskId}/events
```

**查询参数**:
- `limit`: 返回事件数量限制（默认10）
- `offset`: 偏移量（默认0）
- `startTime`: 开始时间过滤
- `endTime`: 结束时间过滤

**响应示例**:
```json
{
  "task_id": "task-001",
  "total_events": 5,
  "events": [
    {
      "eventId": "event_task-001_1",
      "timestamp": "2024-12-01T10:35:00Z",
      "eventType": "ZONE_INTRUSION",
      "confidence": 0.85,
      "location": {
        "x": 150,
        "y": 150
      },
      "metadata": {
        "zoneId": "zone-001",
        "objectId": "person_001"
      }
    }
  ]
}
```

### 2. 服务状态接口

#### 2.1 健康检查

```http
GET /health
```

**响应示例**:
```json
{
  "status": "UP",
  "details": {
    "active_tasks": 3,
    "total_tasks": 10,
    "completed_tasks": 7,
    "failed_tasks": 0,
    "start_time": "2024-12-01T09:00:00Z",
    "last_event_time": "2024-12-01T10:35:00Z"
  },
  "timestamp": 1701425400000
}
```

#### 2.2 服务统计信息

```http
GET /api/v1/stats
```

**响应示例**:
```json
{
  "task_manager": {
    "total_tasks": 10,
    "active_tasks": 3,
    "completed_tasks": 7,
    "failed_tasks": 0,
    "last_event_time": "2024-12-01T10:35:00Z",
    "start_time": "2024-12-01T09:00:00Z"
  },
  "timestamp": 1701425400000
}
```

#### 2.3 服务信息

```http
GET /
```

**响应示例**:
```json
{
  "service": "Video Analysis Inference Service",
  "version": "1.0.0",
  "status": "running",
  "endpoints": {
    "health": "/health",
    "tasks": "/api/v1/tasks",
    "stats": "/api/v1/stats"
  }
}
```

---

## 🚨 错误处理

### 1. 标准错误格式

```json
{
  "success": false,
  "errorCode": "ERROR_CODE",
  "errorMessage": "详细错误描述",
  "details": {
    "field": "具体错误字段",
    "value": "错误值",
    "constraint": "约束条件"
  },
  "timestamp": "2024-12-01T10:30:00Z",
  "path": "/api/v1/scheduler/schedule"
}
```

### 2. 常见错误码

| 错误码 | HTTP状态码 | 说明 | 解决方案 |
|-------|-----------|------|----------|
| `NO_AVAILABLE_SERVICE` | 503 | 没有可用的推理服务 | 等待服务注册或扩容 |
| `QUOTA_EXCEEDED` | 429 | 服务配额已满 | 等待任务完成或选择其他服务 |
| `TASK_SEND_FAILED` | 502 | 任务发送失败 | 检查网络连接，自动重试 |
| `SERVICE_UNREACHABLE` | 503 | 服务不可达 | 检查服务状态和网络 |
| `INVALID_TASK_CONFIG` | 400 | 任务配置无效 | 检查任务配置格式 |
| `TASK_NOT_FOUND` | 404 | 任务不存在 | 检查任务ID是否正确 |
| `DUPLICATE_TASK` | 409 | 任务已存在 | 使用不同的任务ID |
| `ALGORITHM_NOT_SUPPORTED` | 400 | 算法不支持 | 检查算法配置和服务能力 |

### 3. 重试策略

#### 3.1 客户端重试

```javascript
// 指数退避重试示例
async function scheduleTaskWithRetry(taskRequest, maxRetries = 3) {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const response = await fetch('/api/v1/scheduler/schedule', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(taskRequest)
      });
      
      if (response.ok) {
        return await response.json();
      }
      
      if (response.status === 503 && attempt < maxRetries) {
        // 服务不可用，等待后重试
        await sleep(1000 * Math.pow(2, attempt - 1));
        continue;
      }
      
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      
    } catch (error) {
      if (attempt === maxRetries) {
        throw error;
      }
      await sleep(1000 * Math.pow(2, attempt - 1));
    }
  }
}
```

#### 3.2 服务端重试

服务端自动重试机制：
- **失败任务重试**: 每30秒检查并重试失败的任务
- **孤儿任务重新分发**: 自动检测并重新分发孤儿任务
- **服务健康检查**: 定期检查服务可用性

---

## 🔐 安全和认证

### 1. API 认证

目前系统采用内网部署，暂未实现认证机制。生产环境建议添加：

```http
Authorization: Bearer <token>
```

### 2. 请求限制

- **请求频率限制**: 每个客户端每分钟最多100个请求
- **请求大小限制**: 单个请求最大10MB
- **并发连接限制**: 每个服务最多50个并发连接

### 3. 数据验证

- **输入验证**: 所有输入参数进行格式和范围验证
- **SQL注入防护**: 使用参数化查询
- **XSS防护**: 输出内容进行转义处理

---

## 📊 性能指标

### 1. 响应时间

| 接口类型 | 目标响应时间 | 说明 |
|---------|-------------|------|
| 服务注册 | < 500ms | 服务注册和更新 |
| 任务调度 | < 1s | 任务分配和发送 |
| 状态查询 | < 200ms | 任务和服务状态查询 |
| 健康检查 | < 100ms | 服务健康状态检查 |

### 2. 吞吐量

- **任务调度**: 支持每秒100个任务调度请求
- **状态查询**: 支持每秒500个状态查询请求
- **并发任务**: 单个推理服务支持10个并发任务

### 3. 可用性

- **服务可用性**: 99.9%
- **任务成功率**: 95%
- **故障恢复时间**: < 30秒

---

## 📝 版本历史

| 版本 | 发布日期 | 主要变更 |
|------|---------|----------|
| v1.0.0 | 2024-12-01 | 初始版本，基础功能实现 |
| v1.1.0 | 2024-12-15 | 添加孤儿任务清理机制 |
| v1.2.0 | 2025-01-01 | 支持动态算法编排 |

---

## 📞 技术支持

如有API使用问题，请联系：
- **技术文档**: [项目Wiki](https://wiki.example.com)
- **问题反馈**: [GitHub Issues](https://github.com/example/issues)
- **技术交流**: 开发团队微信群

#!/bin/bash

# CV分析系统Docker部署停止脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 停止服务
stop_services() {
    log_info "停止CV分析系统服务..."
    
    # 停止docker-compose服务
    if [ -f "docker-compose.yml" ]; then
        docker-compose down
        
        if [ $? -eq 0 ]; then
            log_success "Docker Compose服务停止成功"
        else
            log_warning "Docker Compose服务停止时出现问题"
        fi
    else
        log_warning "docker-compose.yml文件不存在"
    fi
    
    # 强制停止相关容器
    log_info "检查并停止相关容器..."
    
    containers=$(docker ps -q --filter "name=cv-scheduler" --filter "name=cv-inference-mock")
    if [ ! -z "$containers" ]; then
        log_info "强制停止容器..."
        docker stop $containers
        docker rm $containers
        log_success "容器停止并删除完成"
    else
        log_info "没有找到运行中的相关容器"
    fi
}

# 清理资源
cleanup_resources() {
    if [ "$1" = "--cleanup" ] || [ "$1" = "-c" ]; then
        log_info "清理Docker资源..."
        
        # 删除未使用的镜像
        log_info "删除未使用的镜像..."
        docker image prune -f
        
        # 删除未使用的卷
        log_info "删除未使用的卷..."
        docker volume prune -f
        
        # 删除未使用的网络
        log_info "删除未使用的网络..."
        docker network prune -f
        
        log_success "资源清理完成"
    fi
}

# 显示状态
show_status() {
    echo ""
    log_info "📊 当前状态:"
    
    # 检查容器状态
    containers=$(docker ps -a --filter "name=cv-scheduler" --filter "name=cv-inference-mock" --format "table {{.Names}}\t{{.Status}}")
    if [ ! -z "$containers" ]; then
        echo "$containers"
    else
        echo "  没有找到相关容器"
    fi
    
    echo ""
}

# 显示使用说明
show_usage() {
    echo "使用方法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  (无参数)        停止所有服务"
    echo "  --cleanup, -c   停止服务并清理Docker资源"
    echo "  --help, -h      显示此帮助信息"
    echo ""
}

# 主函数
main() {
    case "${1:-}" in
        "--cleanup"|"-c")
            stop_services
            cleanup_resources "--cleanup"
            show_status
            ;;
        "--help"|"-h")
            show_usage
            ;;
        "")
            stop_services
            show_status
            ;;
        *)
            log_error "未知选项: $1"
            show_usage
            exit 1
            ;;
    esac
    
    log_success "🎉 停止操作完成！"
}

# 执行主函数
main "$@"

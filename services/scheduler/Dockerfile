# Dockerfile
FROM openjdk:17-jdk-slim


# 对于基于 Debian/Ubuntu 的镜像
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

LABEL maintainer="bohua-team"
LABEL description="CV Inference Scheduler"

RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# 复制jar文件
COPY target/cv-scheduler-1.0.0.jar app.jar

# 创建非root用户
RUN groupadd -r scheduler && useradd -r -g scheduler scheduler
RUN chown -R scheduler:scheduler /app
USER scheduler

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8080/actuator/health || exit 1

# 暴露端口
EXPOSE 8080

# 启动命令
ENTRYPOINT ["java", "-jar", "/app/app.jar"]
